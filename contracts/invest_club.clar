(define-constant MEMBERS 5)
(define-constant CONTRIBUTION-STX u30000) ;; $300 = 30000 micro-STX (example rate)
(define-constant MINIMUM-CYCLE-DURATION u12) ;; Minimum lock for 1 year (12 cycles)
(define-constant ADMIN 'SZ2P...yourAdminAddress)

;; Data Structures
(define-map member-contributions { member: principal } { total: uint, last-paid: uint })
(define-map member-rights { member: principal } { active: bool })
(define-map beneficiaries { generation: uint } (list 5 principal))

;; Contract State
(define-data-var total-pool uint u0)
(define-data-var stacking-start-block uint u0)
(define-data-var is-stacking bool false)
(define-data-var current-generation uint u1)
(define-data-var stx-pool-address principal 'ST000000000000000000002AMW42H) ;; Change to pool address
(define-data-var locked-until uint u0)

;; Utility Functions
(define-read-only (is-member (sender principal))
  (default-to false (get active (map-get? member-rights { member: sender })) )
)

(define-read-only (get-total-pool) (var-get total-pool))
(define-read-only (get-current-block) (block-height))

;; ADMIN-ONLY: Add Member (initial generation setup)
(define-public (add-member (member principal))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err u401))
    (map-set member-rights { member: member } { active: true })
    (ok true)
  )
)

;; Public: Contribute to Club
(define-public (contribute)
  (let (
    (sender tx-sender)
    (amount (stx-get-transfer-amount))
  )
    (begin
      (asserts! (is-member sender) (err u403))
      (asserts! (is-eq amount CONTRIBUTION-STX) (err u100)) ;; Ensure correct amount
      (map-set member-contributions
               { member: sender }
               {
                 total: (+ (default-to u0 (get total (map-get? member-contributions { member: sender }))) CONTRIBUTION-STX),
                 last-paid: (get-current-block)
               })
      (var-set total-pool (+ (var-get total-pool) CONTRIBUTION-STX))
      (ok true)
    )
  )
)

;; ADMIN-ONLY: Start Stacking Cycle
(define-public (start-stacking (lock-period uint))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err u401))
    (asserts! (is-eq (var-get is-stacking) false) (err u402))
    (asserts! (>= lock-period MINIMUM-CYCLE-DURATION) (err u405))

    ;; lock STX to a pool via delegation (simulation, as direct PoX not available on-chain)
    (stx-transfer? (var-get total-pool) tx-sender (var-get stx-pool-address))

    (var-set is-stacking true)
    (var-set stacking-start-block (block-height))
    (var-set locked-until (+ (block-height) (* lock-period u120))) ;; approx. 120 blocks per cycle
    (ok true)
  )
)

;; ADMIN-ONLY: Unlock After Stacking Period
(define-public (end-stacking)
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err u401))
    (asserts! (is-eq (var-get is-stacking) true) (err u403))
    (asserts! (>= (block-height) (var-get locked-until)) (err u404))
    (var-set is-stacking false)
    (ok true)
  )
)

;; Read-only: Beneficiary Access for Next Generation
(define-read-only (get-beneficiaries (generation uint))
  (default-to (list) (map-get? beneficiaries generation))
)

;; ADMIN-ONLY: Update to Next Generation
(define-public (set-next-generation (next-gen uint) (next-members (list 5 principal)))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err u401))
    (map-set beneficiaries next-gen next-members)
    (var-set current-generation next-gen)
    (ok true)
  )
)

;; Security Check: Only Admin or Member of Current Generation
(define-read-only (can-access-fund (user principal))
  (or
    (is-eq user ADMIN)
    (contains user (default-to (list) (map-get? beneficiaries (var-get current-generation))))
  )
)

;; ADMIN-ONLY: Withdraw funds (e.g. for investment decision)
(define-public (withdraw-funds (to principal) (amount uint))
  (begin
    (asserts! (can-access-fund tx-sender) (err u401))
    (asserts! (is-eq (var-get is-stacking) false) (err u406))
    (asserts! (<= amount (var-get total-pool)) (err u407))
    (stx-transfer? amount tx-sender to)
    (var-set total-pool (- (var-get total-pool) amount))
    (ok true)
  )
)
